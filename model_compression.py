import torch
import torch.nn as nn
import torch.nn.utils.prune as prune
import numpy as np
from solution import SVDNet

class ModelCompressor:
    """模型压缩工具类"""
    
    def __init__(self, model):
        self.model = model
        
    def structured_pruning(self, pruning_ratio=0.3):
        """结构化剪枝 - 移除整个通道"""
        print(f"Applying structured pruning with ratio: {pruning_ratio}")
        
        # 对卷积层进行通道剪枝
        for name, module in self.model.named_modules():
            if isinstance(module, nn.Conv2d) and module.out_channels > 8:
                # 计算要剪枝的通道数
                num_channels_to_prune = int(module.out_channels * pruning_ratio)
                
                # 随机选择要剪枝的通道 (实际应用中可以基于重要性)
                channels_to_prune = np.random.choice(
                    module.out_channels, 
                    num_channels_to_prune, 
                    replace=False
                )
                
                # 应用结构化剪枝
                prune.ln_structured(
                    module, 
                    name='weight', 
                    amount=num_channels_to_prune, 
                    n=2, 
                    dim=0
                )
                
                print(f"Pruned {num_channels_to_prune} channels from {name}")
    
    def weight_svd_compression(self, compression_ratio=0.5):
        """权重SVD压缩"""
        print(f"Applying weight SVD compression with ratio: {compression_ratio}")
        
        for name, module in self.model.named_modules():
            if isinstance(module, nn.Linear) and module.weight.size(0) > 32:
                # 获取权重矩阵
                weight = module.weight.data
                
                # SVD分解
                U, S, V = torch.svd(weight)
                
                # 计算保留的奇异值数量
                original_rank = min(weight.size(0), weight.size(1))
                compressed_rank = int(original_rank * compression_ratio)
                
                # 压缩
                U_compressed = U[:, :compressed_rank]
                S_compressed = S[:compressed_rank]
                V_compressed = V[:, :compressed_rank]
                
                # 重构权重
                compressed_weight = U_compressed @ torch.diag(S_compressed) @ V_compressed.T
                
                # 更新权重
                module.weight.data = compressed_weight
                
                print(f"Compressed {name}: {original_rank} -> {compressed_rank} rank")
    
    def remove_pruning_masks(self):
        """移除剪枝掩码，使模型永久化"""
        for name, module in self.model.named_modules():
            if hasattr(module, 'weight_mask'):
                prune.remove(module, 'weight')
                print(f"Removed pruning mask from {name}")
    
    def count_parameters(self):
        """计算模型参数数量"""
        total_params = sum(p.numel() for p in self.model.parameters())
        trainable_params = sum(p.numel() for p in self.model.parameters() if p.requires_grad)
        
        print(f"Total parameters: {total_params:,}")
        print(f"Trainable parameters: {trainable_params:,}")
        
        return total_params, trainable_params
    
    def estimate_macs(self, input_shape=(64, 64, 2)):
        """估算MACs (乘加运算次数)"""
        # 简化的MACs估算
        total_macs = 0
        
        # 创建虚拟输入
        dummy_input = torch.randn(1, *input_shape)
        
        # 遍历模型层
        for name, module in self.model.named_modules():
            if isinstance(module, nn.Conv2d):
                # 卷积层MACs = output_elements * kernel_size * input_channels
                kernel_ops = module.kernel_size[0] * module.kernel_size[1] * module.in_channels
                output_elements = (input_shape[1] // module.stride[0]) * (input_shape[2] // module.stride[1])
                macs = kernel_ops * output_elements * module.out_channels
                total_macs += macs
                
            elif isinstance(module, nn.Linear):
                # 全连接层MACs = input_features * output_features
                macs = module.in_features * module.out_features
                total_macs += macs
        
        print(f"Estimated MACs: {total_macs:,}")
        return total_macs

def compress_model(model_path, output_path, pruning_ratio=0.2, svd_ratio=0.6):
    """完整的模型压缩流程"""
    print("Loading model...")
    model = SVDNet()
    
    if model_path and torch.cuda.is_available():
        try:
            model.load_state_dict(torch.load(model_path))
            print(f"Loaded model from {model_path}")
        except:
            print("Could not load model, using random initialization")
    
    # 创建压缩器
    compressor = ModelCompressor(model)
    
    print("\n=== Original Model ===")
    original_params, _ = compressor.count_parameters()
    original_macs = compressor.estimate_macs()
    
    # 应用结构化剪枝
    print("\n=== Applying Structured Pruning ===")
    compressor.structured_pruning(pruning_ratio)
    
    # 应用权重SVD压缩
    print("\n=== Applying Weight SVD Compression ===")
    compressor.weight_svd_compression(svd_ratio)
    
    # 移除剪枝掩码
    compressor.remove_pruning_masks()
    
    print("\n=== Compressed Model ===")
    compressed_params, _ = compressor.count_parameters()
    compressed_macs = compressor.estimate_macs()
    
    # 计算压缩比
    param_reduction = (original_params - compressed_params) / original_params * 100
    macs_reduction = (original_macs - compressed_macs) / original_macs * 100
    
    print(f"\nCompression Results:")
    print(f"Parameter reduction: {param_reduction:.1f}%")
    print(f"MACs reduction: {macs_reduction:.1f}%")
    
    # 保存压缩后的模型
    torch.save(model.state_dict(), output_path)
    print(f"Compressed model saved to {output_path}")
    
    return model

def fine_tune_compressed_model(model, train_loader, epochs=10, lr=1e-4):
    """微调压缩后的模型"""
    print(f"\nFine-tuning compressed model for {epochs} epochs...")
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model = model.to(device)
    
    optimizer = torch.optim.Adam(model.parameters(), lr=lr)
    criterion = nn.MSELoss()
    
    model.train()
    for epoch in range(epochs):
        total_loss = 0.0
        num_batches = 0
        
        for batch_idx, (H_input, H_label) in enumerate(train_loader):
            if batch_idx >= 10:  # 限制微调批次数
                break
                
            H_input = H_input.to(device)
            H_label = H_label.to(device)
            
            optimizer.zero_grad()
            
            # 简化的训练循环
            batch_size = H_input.size(0)
            total_loss_batch = 0.0
            
            for i in range(min(batch_size, 4)):  # 限制batch size
                try:
                    pred_U, pred_S, pred_V = model(H_input[i])
                    # 简化的损失计算
                    loss = criterion(pred_U, torch.zeros_like(pred_U)) * 0.1
                    total_loss_batch += loss
                except Exception as e:
                    print(f"Error in forward pass: {e}")
                    continue
            
            if total_loss_batch > 0:
                avg_loss = total_loss_batch / min(batch_size, 4)
                avg_loss.backward()
                optimizer.step()
                
                total_loss += avg_loss.item()
                num_batches += 1
        
        if num_batches > 0:
            avg_epoch_loss = total_loss / num_batches
            print(f"Epoch {epoch+1}, Loss: {avg_epoch_loss:.6f}")

if __name__ == "__main__":
    # 压缩模型
    compressed_model = compress_model(
        model_path="best_svd_model.pth",  # 如果存在的话
        output_path="compressed_svd_model.pth",
        pruning_ratio=0.2,
        svd_ratio=0.6
    )
    
    print("\nModel compression completed!")
