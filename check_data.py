import numpy as np
import torch

def read_cfg_file(file_path):
    with open(file_path, 'r') as file:
        lines = file.readlines()
        line_fmt = [line.rstrip('\n').split(' ') for line in lines]
    info = line_fmt
    samp_num = int(info[0][0])
    M = int(info[1][0])
    N = int(info[2][0])
    IQ = int(info[3][0])
    R = int(info[4][0])
    return samp_num, M, N, IQ, R

def analyze_svd_data(H_label, H_input=None):
    """分析SVD数据的特性"""
    print(f"Matrix shape: {H_label.shape}")

    # 计算真实的SVD
    if len(H_label.shape) == 3:  # [M, N, 2] format
        H_complex = H_label[:, :, 0] + 1j * H_label[:, :, 1]
    else:
        H_complex = H_label

    U, S, Vh = np.linalg.svd(H_complex, full_matrices=False)
    V = Vh.conj().T

    print(f"Singular values range: [{S.min():.4f}, {S.max():.4f}]")
    print(f"Condition number: {S.max()/S.min():.2f}")
    print(f"Rank (effective): {np.sum(S > S.max() * 1e-10)}")

    # 检查正交性
    U_orth_error = np.linalg.norm(U.conj().T @ U - np.eye(U.shape[1]))
    V_orth_error = np.linalg.norm(V.conj().T @ V - np.eye(V.shape[1]))
    print(f"U orthogonality error: {U_orth_error:.2e}")
    print(f"V orthogonality error: {V_orth_error:.2e}")

    # 重构误差
    H_recon = U @ np.diag(S) @ Vh
    recon_error = np.linalg.norm(H_complex - H_recon) / np.linalg.norm(H_complex)
    print(f"Reconstruction error: {recon_error:.2e}")

    return U, S, V

# Check configuration
print("=== Configuration Analysis ===")
samp_num, M, N, IQ, R = read_cfg_file('CompetitionData1/Round1CfgData1.txt')
print(f"Sample number: {samp_num}")
print(f"M (rows): {M}, N (cols): {N}")
print(f"IQ components: {IQ}")
print(f"Rank R: {R}")

print("\n=== Data Shape Analysis ===")
# Train data
train_data = np.load('CompetitionData1/Round1TrainData1.npy')
print(f'Train Data shape: {train_data.shape}')
print(f'Train Data dtype: {train_data.dtype}')

# Train label
train_label = np.load('CompetitionData1/Round1TrainLabel1.npy')
print(f'Train Label shape: {train_label.shape}')
print(f'Train Label dtype: {train_label.dtype}')

# Test data
test_data = np.load('CompetitionData1/Round1TestData1.npy')
print(f'Test Data shape: {test_data.shape}')
print(f'Test Data dtype: {test_data.dtype}')

print("\n=== Data Range Analysis ===")
print(f'Train Data range: [{train_data.min():.4f}, {train_data.max():.4f}]')
print(f'Train Label range: [{train_label.min():.4f}, {train_label.max():.4f}]')
print(f'Test Data range: [{test_data.min():.4f}, {test_data.max():.4f}]')

print("\n=== SVD Analysis on Sample Data ===")
# 分析第一个样本
sample_label = train_label[0]  # Shape: [M, N, IQ]
sample_input = train_data[0]   # Shape: [M, N, IQ]

print("Label matrix analysis:")
analyze_svd_data(sample_label)

print("\nInput matrix analysis:")
analyze_svd_data(sample_input)

print("\n=== Noise Analysis ===")
# 计算输入和标签之间的差异
diff = sample_input - sample_label
noise_level = np.linalg.norm(diff) / np.linalg.norm(sample_label)
print(f"Relative noise level: {noise_level:.4f}")

print("\n=== Data Statistics ===")
print(f"Train data std: {train_data.std():.4f}")
print(f"Train label std: {train_label.std():.4f}")
print(f"Train data mean: {train_data.mean():.4f}")
print(f"Train label mean: {train_label.mean():.4f}")
