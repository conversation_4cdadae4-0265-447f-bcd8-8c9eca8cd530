import numpy as np

print("Loading data...")

# Load small subset for analysis
train_data = np.load('CompetitionData1/Round1TrainData1.npy')
train_label = np.load('CompetitionData1/Round1TrainLabel1.npy')

print(f'Train Data shape: {train_data.shape}')
print(f'Train Label shape: {train_label.shape}')

# Analyze first sample
sample_input = train_data[0]
sample_label = train_label[0]

print(f'Sample input shape: {sample_input.shape}')
print(f'Sample label shape: {sample_label.shape}')

print(f'Input range: [{sample_input.min():.4f}, {sample_input.max():.4f}]')
print(f'Label range: [{sample_label.min():.4f}, {sample_label.max():.4f}]')

# Check if data is complex (real + imaginary)
print(f'Input last dim (should be 2 for I/Q): {sample_input.shape[-1]}')
print(f'Label last dim (should be 2 for I/Q): {sample_label.shape[-1]}')

print("Data analysis complete.")
