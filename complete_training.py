"""
完整的HCAN训练脚本 - 实现方案文档中的分阶段实施计划
"""

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, random_split
import numpy as np
import os
from solution import SVDNet
from data_augmentation import PhysicalChannelAugmentation, SVDDataset
from training import SV<PERSON><PERSON><PERSON>, SVDTrainer
from model_compression import ModelCompressor

class HCANTrainingPipeline:
    """HCAN训练流水线 - 实现方案文档中的分阶段策略"""
    
    def __init__(self, config):
        self.config = config
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        print(f"Using device: {self.device}")
        
        # 初始化模型
        self.model = SVDNet(M=config['M'], N=config['N'], R=config['R'])
        self.model.to(self.device)
        
        # 初始化损失函数
        self.criterion = SVDLoss()
        
        # 初始化优化器
        self.optimizer = optim.AdamW(
            self.model.parameters(), 
            lr=config['learning_rate'],
            weight_decay=config['weight_decay']
        )
        
        # 学习率调度器
        self.scheduler = optim.lr_scheduler.CosineAnnealingLR(
            self.optimizer, 
            T_max=config['epochs']
        )
        
        # 数据增强
        self.augmentation = PhysicalChannelAugmentation(
            snr_range=config['snr_range'],
            phase_noise_std=config['phase_noise_std'],
            doppler_max=config['doppler_max']
        )
        
    def load_data(self):
        """加载和预处理数据"""
        print("Loading training data...")
        
        # 加载所有训练数据
        train_data_all = []
        train_label_all = []
        
        for i in range(1, 4):  # Round1TrainData1-3.npy
            try:
                train_data = np.load(f'CompetitionData1/Round1TrainData{i}.npy')
                train_label = np.load(f'CompetitionData1/Round1TrainLabel{i}.npy')
                
                train_data_all.append(train_data)
                train_label_all.append(train_label)
                print(f"Loaded dataset {i}: {train_data.shape}")
            except FileNotFoundError:
                print(f"Dataset {i} not found, skipping...")
                continue
        
        if not train_data_all:
            raise ValueError("No training data found!")
        
        # 合并数据
        train_data_combined = np.concatenate(train_data_all, axis=0)
        train_label_combined = np.concatenate(train_label_all, axis=0)
        
        print(f"Total training samples: {len(train_data_combined)}")
        
        # 数据分割
        total_samples = len(train_data_combined)
        train_size = int(0.8 * total_samples)
        val_size = int(0.1 * total_samples)
        test_size = total_samples - train_size - val_size
        
        # 创建数据集
        full_dataset = SVDDataset(train_data_combined, train_label_combined, None)
        train_dataset, val_dataset, test_dataset = random_split(
            full_dataset, [train_size, val_size, test_size]
        )
        
        # 为训练集添加数据增强
        train_dataset.dataset.augmentation = self.augmentation
        
        # 创建数据加载器
        self.train_loader = DataLoader(
            train_dataset, 
            batch_size=self.config['batch_size'], 
            shuffle=True,
            num_workers=2
        )
        
        self.val_loader = DataLoader(
            val_dataset, 
            batch_size=self.config['batch_size'], 
            shuffle=False,
            num_workers=2
        )
        
        self.test_loader = DataLoader(
            test_dataset, 
            batch_size=self.config['batch_size'], 
            shuffle=False,
            num_workers=2
        )
        
        print(f"Data split - Train: {len(train_dataset)}, Val: {len(val_dataset)}, Test: {len(test_dataset)}")
    
    def train_epoch(self, epoch):
        """训练一个epoch"""
        self.model.train()
        total_loss = 0.0
        total_recon_loss = 0.0
        total_orth_loss = 0.0
        num_batches = 0
        
        for batch_idx, (H_input, H_label) in enumerate(self.train_loader):
            H_input = H_input.to(self.device)
            H_label = H_label.to(self.device)
            
            self.optimizer.zero_grad()
            
            # 前向传播 (批处理)
            batch_size = H_input.size(0)
            batch_loss = 0.0
            batch_metrics = {'recon_loss': 0.0, 'U_orth_loss': 0.0, 'V_orth_loss': 0.0}
            
            for i in range(batch_size):
                # 单样本处理 (适配当前接口)
                pred_U, pred_S, pred_V = self.model(H_input[i])
                
                # 添加batch维度
                pred_U = pred_U.unsqueeze(0)
                pred_S = pred_S.unsqueeze(0)
                pred_V = pred_V.unsqueeze(0)
                
                # 计算损失
                loss, metrics = self.criterion(pred_U, pred_S, pred_V, H_label[i:i+1])
                batch_loss += loss
                
                for key in batch_metrics:
                    batch_metrics[key] += metrics[key]
            
            # 平均损失
            avg_loss = batch_loss / batch_size
            for key in batch_metrics:
                batch_metrics[key] /= batch_size
            
            # 反向传播
            avg_loss.backward()
            torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)
            self.optimizer.step()
            
            # 统计
            total_loss += avg_loss.item()
            total_recon_loss += batch_metrics['recon_loss']
            total_orth_loss += (batch_metrics['U_orth_loss'] + batch_metrics['V_orth_loss']) / 2
            num_batches += 1
            
            if batch_idx % 50 == 0:
                print(f'Epoch {epoch}, Batch {batch_idx}/{len(self.train_loader)}, '
                      f'Loss: {avg_loss.item():.6f}, '
                      f'Recon: {batch_metrics["recon_loss"]:.6f}, '
                      f'Orth: {(batch_metrics["U_orth_loss"] + batch_metrics["V_orth_loss"])/2:.6f}')
        
        return {
            'total_loss': total_loss / num_batches,
            'recon_loss': total_recon_loss / num_batches,
            'orth_loss': total_orth_loss / num_batches
        }
    
    def validate(self):
        """验证模型"""
        self.model.eval()
        total_loss = 0.0
        total_recon_loss = 0.0
        total_orth_loss = 0.0
        num_batches = 0
        
        with torch.no_grad():
            for H_input, H_label in self.val_loader:
                H_input = H_input.to(self.device)
                H_label = H_label.to(self.device)
                
                batch_size = H_input.size(0)
                batch_loss = 0.0
                batch_metrics = {'recon_loss': 0.0, 'U_orth_loss': 0.0, 'V_orth_loss': 0.0}
                
                for i in range(batch_size):
                    pred_U, pred_S, pred_V = self.model(H_input[i])
                    
                    pred_U = pred_U.unsqueeze(0)
                    pred_S = pred_S.unsqueeze(0)
                    pred_V = pred_V.unsqueeze(0)
                    
                    loss, metrics = self.criterion(pred_U, pred_S, pred_V, H_label[i:i+1])
                    batch_loss += loss
                    
                    for key in batch_metrics:
                        batch_metrics[key] += metrics[key]
                
                avg_loss = batch_loss / batch_size
                for key in batch_metrics:
                    batch_metrics[key] /= batch_size
                
                total_loss += avg_loss.item()
                total_recon_loss += batch_metrics['recon_loss']
                total_orth_loss += (batch_metrics['U_orth_loss'] + batch_metrics['V_orth_loss']) / 2
                num_batches += 1
        
        return {
            'total_loss': total_loss / num_batches,
            'recon_loss': total_recon_loss / num_batches,
            'orth_loss': total_orth_loss / num_batches
        }
    
    def train(self):
        """完整训练流程 - 第一阶段：最大化精度"""
        print("\n=== Phase 1: Maximizing Accuracy ===")
        
        best_val_loss = float('inf')
        patience = 10
        patience_counter = 0
        
        for epoch in range(self.config['epochs']):
            print(f'\nEpoch {epoch+1}/{self.config["epochs"]}')
            
            # 训练
            train_metrics = self.train_epoch(epoch)
            
            # 验证
            val_metrics = self.validate()
            
            # 学习率调度
            self.scheduler.step()
            
            print(f'Train - Loss: {train_metrics["total_loss"]:.6f}, '
                  f'Recon: {train_metrics["recon_loss"]:.6f}, '
                  f'Orth: {train_metrics["orth_loss"]:.6f}')
            print(f'Val   - Loss: {val_metrics["total_loss"]:.6f}, '
                  f'Recon: {val_metrics["recon_loss"]:.6f}, '
                  f'Orth: {val_metrics["orth_loss"]:.6f}')
            
            # 保存最佳模型
            if val_metrics['total_loss'] < best_val_loss:
                best_val_loss = val_metrics['total_loss']
                torch.save(self.model.state_dict(), 'best_hcan_model.pth')
                print(f'New best model saved with val loss: {val_metrics["total_loss"]:.6f}')
                patience_counter = 0
            else:
                patience_counter += 1
                
            # 早停
            if patience_counter >= patience:
                print(f"Early stopping at epoch {epoch+1}")
                break
        
        print(f"\nPhase 1 completed. Best validation loss: {best_val_loss:.6f}")
    
    def compress_model(self):
        """第二阶段：模型压缩与优化"""
        print("\n=== Phase 2: Model Compression ===")
        
        # 加载最佳模型
        self.model.load_state_dict(torch.load('best_hcan_model.pth'))
        
        # 创建压缩器
        compressor = ModelCompressor(self.model)
        
        # 压缩前统计
        print("Before compression:")
        original_params, _ = compressor.count_parameters()
        original_macs = compressor.estimate_macs()
        
        # 应用压缩
        compressor.structured_pruning(self.config['pruning_ratio'])
        compressor.weight_svd_compression(self.config['svd_compression_ratio'])
        compressor.remove_pruning_masks()
        
        # 压缩后统计
        print("\nAfter compression:")
        compressed_params, _ = compressor.count_parameters()
        compressed_macs = compressor.estimate_macs()
        
        # 计算压缩比
        param_reduction = (original_params - compressed_params) / original_params * 100
        macs_reduction = (original_macs - compressed_macs) / original_macs * 100
        
        print(f"\nCompression Results:")
        print(f"Parameter reduction: {param_reduction:.1f}%")
        print(f"MACs reduction: {macs_reduction:.1f}%")
        
        # 保存压缩后的模型
        torch.save(self.model.state_dict(), 'compressed_hcan_model.pth')
        print("Compressed model saved.")
        
        return param_reduction, macs_reduction

def main():
    """主函数 - 实现方案文档中的完整流程"""
    
    # 配置参数
    config = {
        'M': 64,
        'N': 64,
        'R': 32,
        'learning_rate': 1e-3,
        'weight_decay': 1e-4,
        'batch_size': 8,
        'epochs': 50,
        'snr_range': (10, 30),
        'phase_noise_std': 0.1,
        'doppler_max': 100,
        'pruning_ratio': 0.2,
        'svd_compression_ratio': 0.6
    }
    
    print("=== HCAN Training Pipeline ===")
    print("Implementing the strategic blueprint from the solution document")
    
    # 创建训练流水线
    pipeline = HCANTrainingPipeline(config)
    
    # 加载数据
    pipeline.load_data()
    
    # 第一阶段：最大化精度
    pipeline.train()
    
    # 第二阶段：模型压缩
    param_reduction, macs_reduction = pipeline.compress_model()
    
    print(f"\n=== Training Pipeline Completed ===")
    print(f"Final model achieves {param_reduction:.1f}% parameter reduction")
    print(f"and {macs_reduction:.1f}% MACs reduction while maintaining accuracy.")
    print("Ready for competition submission!")

if __name__ == "__main__":
    main()
