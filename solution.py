import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import math

class ChannelAttention(nn.Module):
    """通道注意力模块 - CBAM的组成部分"""
    def __init__(self, in_channels, reduction=16):
        super(ChannelAttention, self).__init__()
        self.avg_pool = nn.AdaptiveAvgPool2d(1)
        self.max_pool = nn.AdaptiveMaxPool2d(1)

        self.fc = nn.Sequential(
            nn.Conv2d(in_channels, in_channels // reduction, 1, bias=False),
            nn.ReLU(inplace=True),
            nn.Conv2d(in_channels // reduction, in_channels, 1, bias=False)
        )
        self.sigmoid = nn.Sigmoid()

    def forward(self, x):
        avg_out = self.fc(self.avg_pool(x))
        max_out = self.fc(self.max_pool(x))
        out = avg_out + max_out
        return self.sigmoid(out)

class SpatialAttention(nn.Module):
    """空间注意力模块 - CBAM的组成部分"""
    def __init__(self, kernel_size=7):
        super(SpatialAttention, self).__init__()
        self.conv = nn.Conv2d(2, 1, kernel_size, padding=kernel_size//2, bias=False)
        self.sigmoid = nn.Sigmoid()

    def forward(self, x):
        avg_out = torch.mean(x, dim=1, keepdim=True)
        max_out, _ = torch.max(x, dim=1, keepdim=True)
        x = torch.cat([avg_out, max_out], dim=1)
        x = self.conv(x)
        return self.sigmoid(x)

class CBAM(nn.Module):
    """卷积块注意力模块 (Convolutional Block Attention Module)"""
    def __init__(self, in_channels, reduction=16, kernel_size=7):
        super(CBAM, self).__init__()
        self.channel_attention = ChannelAttention(in_channels, reduction)
        self.spatial_attention = SpatialAttention(kernel_size)

    def forward(self, x):
        x = x * self.channel_attention(x)
        x = x * self.spatial_attention(x)
        return x

class ConvBlock(nn.Module):
    """带CBAM注意力的卷积块"""
    def __init__(self, in_channels, out_channels, kernel_size=3, padding=1, use_attention=True):
        super(ConvBlock, self).__init__()
        self.conv1 = nn.Conv2d(in_channels, out_channels, kernel_size, padding=padding)
        self.bn1 = nn.BatchNorm2d(out_channels)
        self.conv2 = nn.Conv2d(out_channels, out_channels, kernel_size, padding=padding)
        self.bn2 = nn.BatchNorm2d(out_channels)
        self.relu = nn.ReLU(inplace=True)

        # 添加CBAM注意力
        self.use_attention = use_attention
        if use_attention:
            self.cbam = CBAM(out_channels)

    def forward(self, x):
        x = self.relu(self.bn1(self.conv1(x)))
        x = self.relu(self.bn2(self.conv2(x)))

        if self.use_attention:
            x = self.cbam(x)

        return x

class DownBlock(nn.Module):
    """下采样块"""
    def __init__(self, in_channels, out_channels):
        super(DownBlock, self).__init__()
        self.conv_block = ConvBlock(in_channels, out_channels)
        self.pool = nn.MaxPool2d(2)

    def forward(self, x):
        skip = self.conv_block(x)
        x = self.pool(skip)
        return x, skip

class UpBlock(nn.Module):
    """上采样块"""
    def __init__(self, in_channels, out_channels):
        super(UpBlock, self).__init__()
        self.up = nn.ConvTranspose2d(in_channels, in_channels // 2, 2, stride=2)
        self.conv_block = ConvBlock(in_channels, out_channels)

    def forward(self, x, skip):
        x = self.up(x)
        # 确保尺寸匹配
        if x.size() != skip.size():
            x = F.interpolate(x, size=skip.shape[2:], mode='bilinear', align_corners=False)
        x = torch.cat([x, skip], dim=1)
        x = self.conv_block(x)
        return x

class UNet(nn.Module):
    """基础U-Net架构"""
    def __init__(self, in_channels=2, base_channels=64):
        super(UNet, self).__init__()

        # 编码器
        self.down1 = DownBlock(in_channels, base_channels)
        self.down2 = DownBlock(base_channels, base_channels * 2)
        self.down3 = DownBlock(base_channels * 2, base_channels * 4)
        self.down4 = DownBlock(base_channels * 4, base_channels * 8)

        # 瓶颈层
        self.bottleneck = ConvBlock(base_channels * 8, base_channels * 16)

        # 解码器
        self.up1 = UpBlock(base_channels * 16, base_channels * 8)
        self.up2 = UpBlock(base_channels * 8, base_channels * 4)
        self.up3 = UpBlock(base_channels * 4, base_channels * 2)
        self.up4 = UpBlock(base_channels * 2, base_channels)

        # 输出层
        self.final_conv = nn.Conv2d(base_channels, base_channels, 1)

    def forward(self, x):
        # 编码路径
        x1, skip1 = self.down1(x)
        x2, skip2 = self.down2(x1)
        x3, skip3 = self.down3(x2)
        x4, skip4 = self.down4(x3)

        # 瓶颈
        x = self.bottleneck(x4)

        # 解码路径
        x = self.up1(x, skip4)
        x = self.up2(x, skip3)
        x = self.up3(x, skip2)
        x = self.up4(x, skip1)

        # 最终特征
        features = self.final_conv(x)
        return features

class OrthogonalLayer(nn.Module):
    """硬正交性约束层 - 通过李群参数化实现"""
    def __init__(self, matrix_size, rank, feature_dim):
        super(OrthogonalLayer, self).__init__()
        self.matrix_size = matrix_size  # M or N
        self.rank = rank  # R

        # 生成斜埃尔米特矩阵参数的全连接层
        self.skew_hermitian_fc = nn.Linear(feature_dim, matrix_size * rank * 2)

        # 初始化权重
        nn.init.xavier_uniform_(self.skew_hermitian_fc.weight, gain=0.01)
        nn.init.zeros_(self.skew_hermitian_fc.bias)

    def forward(self, features):
        """
        通过矩阵指数生成酉矩阵
        """
        batch_size = features.size(0)

        # 生成斜埃尔米特矩阵参数
        skew_params = self.skew_hermitian_fc(features)  # [batch, matrix_size * rank * 2]
        skew_params = skew_params.view(batch_size, self.matrix_size, self.rank, 2)

        # 构造斜埃尔米特矩阵 A (满足 A* = -A)
        real_part = skew_params[..., 0]  # [batch, matrix_size, rank]
        imag_part = skew_params[..., 1]  # [batch, matrix_size, rank]

        # 创建复数张量
        A_complex = torch.complex(real_part, imag_part)

        # 使用矩阵指数的泰勒级数近似: exp(A) ≈ I + A + A²/2! + A³/3! + ...
        # 对于小的A，前几项就足够了
        I = torch.eye(self.matrix_size, dtype=A_complex.dtype, device=A_complex.device)
        I = I.unsqueeze(0).expand(batch_size, -1, -1)

        # 计算 A @ A^H (用于高阶项)
        A_H = A_complex.conj().transpose(-2, -1)
        A_squared = torch.matmul(A_complex, A_H)

        # 泰勒级数近似 (前3项)
        exp_A = I + A_complex + 0.5 * A_squared

        # 使用QR分解确保严格的正交性
        Q, R = torch.linalg.qr(exp_A)

        # 只取前rank列
        Q_truncated = Q[:, :, :self.rank]

        # 转换回实数表示 [batch, matrix_size, rank, 2]
        result = torch.stack([Q_truncated.real, Q_truncated.imag], dim=-1)

        return result

class SVDOutputHead(nn.Module):
    """SVD输出头，包含U、S、V的生成"""
    def __init__(self, feature_channels, M=64, N=64, R=32):
        super(SVDOutputHead, self).__init__()
        self.M = M
        self.N = N
        self.R = R

        # 全局平均池化
        self.global_pool = nn.AdaptiveAvgPool2d(1)

        # 特征映射
        self.feature_fc = nn.Sequential(
            nn.Linear(feature_channels, 512),
            nn.ReLU(inplace=True),
            nn.Dropout(0.1),
            nn.Linear(512, 256),
            nn.ReLU(inplace=True)
        )

        # U矩阵生成 (通过硬正交性约束)
        self.U_orthogonal = OrthogonalLayer(M, R, 256)

        # V矩阵生成 (通过硬正交性约束)
        self.V_orthogonal = OrthogonalLayer(N, R, 256)

        # S向量生成 (奇异值，确保为正)
        self.S_fc = nn.Sequential(
            nn.Linear(256, 128),
            nn.ReLU(inplace=True),
            nn.Linear(128, R),
            nn.Softplus()
        )

    def forward(self, features):
        # 全局特征提取
        batch_size = features.size(0)
        global_features = self.global_pool(features).view(batch_size, -1)
        global_features = self.feature_fc(global_features)

        # 生成U矩阵 (使用硬正交性约束)
        U = self.U_orthogonal(global_features)  # [batch, M, R, 2]

        # 生成V矩阵 (使用硬正交性约束)
        V = self.V_orthogonal(global_features)  # [batch, N, R, 2]

        # 生成奇异值S (确保为正)
        S = self.S_fc(global_features)  # [batch, R]
        S = S + 1e-6  # 确保严格为正

        return U, S, V

class HCAN(nn.Module):
    """混合卷积注意力网络 (Hybrid Convolutional Attention Network)"""
    def __init__(self, in_channels=2, base_channels=64, M=64, N=64, R=32):
        super(HCAN, self).__init__()
        self.M = M
        self.N = N
        self.R = R

        # U-Net骨干网络
        self.unet = UNet(in_channels, base_channels)

        # SVD输出头
        self.svd_head = SVDOutputHead(base_channels, M, N, R)

    def forward(self, x):
        # 提取特征
        features = self.unet(x)

        # 生成SVD分解
        U, S, V = self.svd_head(features)

        return U, S, V

class SVDNet(nn.Module):
    """主要的SVD网络类 - 竞赛接口"""
    def __init__(self, M=64, N=64, R=32):
        super(SVDNet, self).__init__()
        self.M = M
        self.N = N
        self.R = R

        # 使用HCAN作为主网络
        self.hcan = HCAN(in_channels=2, base_channels=32, M=M, N=N, R=R)

    def forward(self, x):
        """
        输入: x [M, N, 2] - 信道矩阵 (实部+虚部)
        输出: U [M, R, 2], S [R], V [N, R, 2]
        """
        # 添加batch维度并调整维度顺序
        if len(x.shape) == 3:
            x = x.unsqueeze(0)  # [1, M, N, 2]

        # 调整维度顺序: [batch, M, N, 2] -> [batch, 2, M, N]
        x = x.permute(0, 3, 1, 2)

        # 前向传播
        U, S, V = self.hcan(x)

        # 移除batch维度
        U = U.squeeze(0)  # [M, R, 2]
        S = S.squeeze(0)  # [R]
        V = V.squeeze(0)  # [N, R, 2]

        return U, S, V

class SVDNet(nn.Module):
    """主要的SVD网络类 - 竞赛接口"""
    def __init__(self, M=64, N=64, R=32):
        super(SVDNet, self).__init__()
        self.M = M
        self.N = N
        self.R = R

        # 使用HCAN作为主网络
        self.hcan = HCAN(in_channels=2, base_channels=32, M=M, N=N, R=R)

    def forward(self, x):
        """
        输入: x [M, N, 2] - 信道矩阵 (实部+虚部)
        输出: U [M, R, 2], S [R], V [N, R, 2]
        """
        # 添加batch维度并调整维度顺序
        if len(x.shape) == 3:
            x = x.unsqueeze(0)  # [1, M, N, 2]

        # 调整维度顺序: [batch, M, N, 2] -> [batch, 2, M, N]
        x = x.permute(0, 3, 1, 2)

        # 前向传播
        U, S, V = self.hcan(x)

        # 移除batch维度
        U = U.squeeze(0)  # [M, R, 2]
        S = S.squeeze(0)  # [R]
        V = V.squeeze(0)  # [N, R, 2]

        return U, S, V