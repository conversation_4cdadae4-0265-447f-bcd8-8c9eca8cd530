import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np

class SimpleSVDNet(nn.Module):
    """简化的SVD网络 - 确保基本功能正常"""
    def __init__(self, M=64, N=64, R=32):
        super(SimpleSVDNet, self).__init__()
        self.M = M
        self.N = N
        self.R = R

        # 输入维度: M * N * 2 (实部+虚部)
        input_dim = M * N * 2

        # 编码器
        self.encoder = nn.Sequential(
            nn.Linear(input_dim, 1024),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(1024, 512),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(512, 256),
            nn.ReLU()
        )

        # U矩阵输出头 [M, R, 2]
        self.U_head = nn.Sequential(
            nn.Linear(256, M * R * 2),
            nn.Tanh()  # 限制输出范围
        )

        # V矩阵输出头 [N, R, 2]
        self.V_head = nn.Sequential(
            nn.Linear(256, N * R * 2),
            nn.Tanh()  # 限制输出范围
        )

        # S向量输出头 [R]
        self.S_head = nn.Sequential(
            nn.Linear(256, R),
            nn.Softplus()  # 确保为正
        )

    def normalize_matrix(self, matrix):
        """归一化矩阵列向量"""
        # matrix: [M/N, R, 2]
        real = matrix[..., 0]  # [M/N, R]
        imag = matrix[..., 1]  # [M/N, R]

        # 计算每列的模长
        norm = torch.sqrt(real**2 + imag**2 + 1e-8)  # [M/N, R]

        # 归一化
        real_norm = real / norm
        imag_norm = imag / norm

        return torch.stack([real_norm, imag_norm], dim=-1)

    def forward(self, x):
        """
        输入: x [M, N, 2] - 信道矩阵
        输出: U [M, R, 2], S [R], V [N, R, 2]
        """
        # 展平输入
        x_flat = x.view(-1)  # [M*N*2]

        # 编码
        features = self.encoder(x_flat)  # [256]

        # 生成U矩阵
        U_flat = self.U_head(features)  # [M*R*2]
        U = U_flat.view(self.M, self.R, 2)  # [M, R, 2]
        U = self.normalize_matrix(U)

        # 生成V矩阵
        V_flat = self.V_head(features)  # [N*R*2]
        V = V_flat.view(self.N, self.R, 2)  # [N, R, 2]
        V = self.normalize_matrix(V)

        # 生成S向量
        S = self.S_head(features)  # [R]
        S = S + 1e-6  # 确保为正

        return U, S, V

class SVDNet(nn.Module):
    """主要的SVD网络类 - 竞赛接口"""
    def __init__(self, M=64, N=64, R=32):
        super(SVDNet, self).__init__()
        self.simple_net = SimpleSVDNet(M, N, R)

    def forward(self, x):
        """
        输入: x [M, N, 2] - 信道矩阵 (实部+虚部)
        输出: U [M, R, 2], S [R], V [N, R, 2]
        """
        return self.simple_net(x)

class SVDNet(nn.Module):
    """主要的SVD网络类 - 竞赛接口"""
    def __init__(self, M=64, N=64, R=32):
        super(SVDNet, self).__init__()
        self.M = M
        self.N = N
        self.R = R

        # 使用HCAN作为主网络
        self.hcan = HCAN(in_channels=2, base_channels=32, M=M, N=N, R=R)

    def forward(self, x):
        """
        输入: x [M, N, 2] - 信道矩阵 (实部+虚部)
        输出: U [M, R, 2], S [R], V [N, R, 2]
        """
        # 添加batch维度并调整维度顺序
        if len(x.shape) == 3:
            x = x.unsqueeze(0)  # [1, M, N, 2]

        # 调整维度顺序: [batch, M, N, 2] -> [batch, 2, M, N]
        x = x.permute(0, 3, 1, 2)

        # 前向传播
        U, S, V = self.hcan(x)

        # 移除batch维度
        U = U.squeeze(0)  # [M, R, 2]
        S = S.squeeze(0)  # [R]
        V = V.squeeze(0)  # [N, R, 2]

        return U, S, V