import torch
import numpy as np
from solution import SVDNet

def test_svd_net():
    """测试SVDNet的基本功能"""
    print("Testing SVDNet...")
    
    # 创建模型
    model = SVDNet(M=64, N=64, R=32)
    model.eval()
    
    # 创建测试输入
    H_test = torch.randn(64, 64, 2)  # [M, N, 2]
    
    print(f"Input shape: {H_test.shape}")
    
    try:
        # 前向传播
        with torch.no_grad():
            U, S, V = model(H_test)
        
        print(f"Output shapes:")
        print(f"U: {U.shape}")  # 应该是 [64, 32, 2]
        print(f"S: {S.shape}")  # 应该是 [32]
        print(f"V: {V.shape}")  # 应该是 [64, 32, 2]
        
        # 检查输出范围
        print(f"\nOutput ranges:")
        print(f"U range: [{U.min():.4f}, {U.max():.4f}]")
        print(f"S range: [{S.min():.4f}, {S.max():.4f}]")
        print(f"V range: [{V.min():.4f}, {V.max():.4f}]")
        
        # 检查正交性
        U_complex = torch.complex(U[..., 0], U[..., 1])
        V_complex = torch.complex(V[..., 0], V[..., 1])
        
        U_orth_error = torch.norm(U_complex.conj().T @ U_complex - torch.eye(32))
        V_orth_error = torch.norm(V_complex.conj().T @ V_complex - torch.eye(32))
        
        print(f"\nOrthogonality errors:")
        print(f"U orthogonality error: {U_orth_error:.6f}")
        print(f"V orthogonality error: {V_orth_error:.6f}")
        
        # 重构测试
        H_recon_complex = U_complex @ torch.diag(S) @ V_complex.conj().T
        H_recon = torch.stack([H_recon_complex.real, H_recon_complex.imag], dim=-1)
        
        recon_error = torch.norm(H_test - H_recon) / torch.norm(H_test)
        print(f"Reconstruction error: {recon_error:.6f}")
        
        print("\n✓ SVDNet test passed!")
        return True
        
    except Exception as e:
        print(f"✗ SVDNet test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_demo_compatibility():
    """测试与demo_code.py的兼容性"""
    print("\nTesting demo compatibility...")
    
    try:
        # 模拟demo_code.py中的使用方式
        device = 'cpu'
        model = SVDNet().to(device)
        
        # 创建测试数据 (模拟从.npy文件加载的数据)
        H_data = torch.randn(64, 64, 2)  # [M, N, IQ]
        
        with torch.no_grad():
            H_data = H_data.to(device)
            U_out, S_out, V_out = model(H_data)
        
        # 转换为numpy (模拟demo_code.py中的操作)
        U_numpy = U_out.cpu().numpy()
        S_numpy = S_out.cpu().numpy()
        V_numpy = V_out.cpu().numpy()
        
        print(f"Numpy output shapes:")
        print(f"U: {U_numpy.shape}")
        print(f"S: {S_numpy.shape}")
        print(f"V: {V_numpy.shape}")
        
        # 检查数据类型
        print(f"Output dtypes:")
        print(f"U: {U_numpy.dtype}")
        print(f"S: {S_numpy.dtype}")
        print(f"V: {V_numpy.dtype}")
        
        print("✓ Demo compatibility test passed!")
        return True
        
    except Exception as e:
        print(f"✗ Demo compatibility test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def count_parameters():
    """统计模型参数数量"""
    model = SVDNet()
    
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    
    print(f"\nModel Statistics:")
    print(f"Total parameters: {total_params:,}")
    print(f"Trainable parameters: {trainable_params:,}")
    
    # 估算模型大小 (MB)
    param_size = total_params * 4 / (1024 * 1024)  # 假设float32
    print(f"Estimated model size: {param_size:.2f} MB")
    
    return total_params

def main():
    """主测试函数"""
    print("=" * 50)
    print("SVDNet Solution Testing")
    print("=" * 50)
    
    # 基本功能测试
    test1_passed = test_svd_net()
    
    # 兼容性测试
    test2_passed = test_demo_compatibility()
    
    # 参数统计
    param_count = count_parameters()
    
    print("\n" + "=" * 50)
    print("Test Summary:")
    print(f"Basic functionality: {'PASS' if test1_passed else 'FAIL'}")
    print(f"Demo compatibility: {'PASS' if test2_passed else 'FAIL'}")
    print(f"Parameter count: {param_count:,}")
    
    if test1_passed and test2_passed:
        print("\n🎉 All tests passed! Solution is ready.")
    else:
        print("\n❌ Some tests failed. Please check the implementation.")

if __name__ == "__main__":
    main()
