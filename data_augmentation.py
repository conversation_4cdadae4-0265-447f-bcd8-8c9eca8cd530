import torch
import numpy as np

class PhysicalChannelAugmentation:
    """基于物理信道特性的数据增强 - 实现方案文档中的增强策略"""

    def __init__(self, snr_range=(10, 30), phase_noise_std=0.1, doppler_max=100,
                 hardware_impairment_std=0.05):
        self.snr_range = snr_range
        self.phase_noise_std = phase_noise_std
        self.doppler_max = doppler_max
        self.hardware_impairment_std = hardware_impairment_std
        
    def add_awgn(self, H_clean, snr_db):
        """添加加性复高斯白噪声"""
        # 计算信号功率
        signal_power = torch.mean(torch.abs(H_clean) ** 2)
        
        # 计算噪声功率
        snr_linear = 10 ** (snr_db / 10)
        noise_power = signal_power / snr_linear
        
        # 生成复高斯噪声
        noise_real = torch.randn_like(H_clean[..., 0]) * torch.sqrt(noise_power / 2)
        noise_imag = torch.randn_like(H_clean[..., 1]) * torch.sqrt(noise_power / 2)
        noise = torch.stack([noise_real, noise_imag], dim=-1)
        
        return H_clean + noise
    
    def add_phase_noise(self, H_clean, std=None):
        """添加相位噪声 (维纳过程)"""
        if std is None:
            std = self.phase_noise_std
            
        # 生成相位噪声 (随机游走)
        M, N = H_clean.shape[:2]
        phase_noise = torch.cumsum(torch.randn(M, N) * std, dim=0)
        
        # 应用相位旋转
        cos_phase = torch.cos(phase_noise)
        sin_phase = torch.sin(phase_noise)
        
        # 复数乘法: (a + bi) * (cos + i*sin)
        real_part = H_clean[..., 0] * cos_phase - H_clean[..., 1] * sin_phase
        imag_part = H_clean[..., 0] * sin_phase + H_clean[..., 1] * cos_phase
        
        return torch.stack([real_part, imag_part], dim=-1)
    
    def add_doppler_shift(self, H_clean, max_doppler=None):
        """添加多普勒频移"""
        if max_doppler is None:
            max_doppler = self.doppler_max
            
        M, N = H_clean.shape[:2]
        
        # 随机多普勒频率
        doppler_freq = torch.rand(M, N) * 2 * max_doppler - max_doppler
        
        # 假设时间步长
        t = 1.0  # 归一化时间
        
        # 多普勒相移
        doppler_phase = 2 * np.pi * doppler_freq * t
        
        cos_doppler = torch.cos(doppler_phase)
        sin_doppler = torch.sin(doppler_phase)
        
        # 应用多普勒频移
        real_part = H_clean[..., 0] * cos_doppler - H_clean[..., 1] * sin_doppler
        imag_part = H_clean[..., 0] * sin_doppler + H_clean[..., 1] * cos_doppler
        
        return torch.stack([real_part, imag_part], dim=-1)
    
    def add_hardware_impairments(self, H_clean, std=None):
        """添加硬件非理想因素 - IQ不平衡、载波泄漏等"""
        if std is None:
            std = self.hardware_impairment_std

        M, N = H_clean.shape[:2]

        # IQ不平衡 - 幅度和相位不匹配
        amplitude_imbalance = 1 + torch.randn(M, N) * std * 0.1
        phase_imbalance = torch.randn(M, N) * std * 0.1

        # 载波泄漏 - DC偏移
        dc_offset_real = torch.randn(M, N) * std * 0.05
        dc_offset_imag = torch.randn(M, N) * std * 0.05

        # 应用硬件损伤
        real_part = H_clean[..., 0] * amplitude_imbalance + dc_offset_real
        imag_part = H_clean[..., 1] * amplitude_imbalance * torch.cos(phase_imbalance) + dc_offset_imag

        return torch.stack([real_part, imag_part], dim=-1)

    def add_channel_estimation_error(self, H_clean, error_std=0.02):
        """添加信道估计误差"""
        # 模拟信道估计中的随机误差
        estimation_error_real = torch.randn_like(H_clean[..., 0]) * error_std
        estimation_error_imag = torch.randn_like(H_clean[..., 1]) * error_std

        estimation_error = torch.stack([estimation_error_real, estimation_error_imag], dim=-1)

        return H_clean + estimation_error

    def augment(self, H_clean):
        """综合数据增强 - 实现方案文档中的物理信息增强策略"""
        H_aug = H_clean.clone()

        # 1. 加性复高斯白噪声 (80%概率)
        if torch.rand(1) < 0.8:
            snr = torch.rand(1) * (self.snr_range[1] - self.snr_range[0]) + self.snr_range[0]
            H_aug = self.add_awgn(H_aug, snr.item())

        # 2. 相位噪声模拟 (50%概率)
        if torch.rand(1) < 0.5:
            H_aug = self.add_phase_noise(H_aug)

        # 3. 多普勒频移模拟 (30%概率)
        if torch.rand(1) < 0.3:
            H_aug = self.add_doppler_shift(H_aug)

        # 4. 硬件非理想因素 (40%概率)
        if torch.rand(1) < 0.4:
            H_aug = self.add_hardware_impairments(H_aug)

        # 5. 信道估计误差 (60%概率)
        if torch.rand(1) < 0.6:
            H_aug = self.add_channel_estimation_error(H_aug)

        return H_aug

class SVDDataset(torch.utils.data.Dataset):
    """SVD数据集类"""
    
    def __init__(self, H_input, H_label, augmentation=None):
        self.H_input = H_input
        self.H_label = H_label
        self.augmentation = augmentation
        
    def __len__(self):
        return len(self.H_input)
    
    def __getitem__(self, idx):
        h_input = torch.tensor(self.H_input[idx], dtype=torch.float32)
        h_label = torch.tensor(self.H_label[idx], dtype=torch.float32)
        
        # 应用数据增强
        if self.augmentation is not None:
            h_input = self.augmentation.augment(h_input)
            
        return h_input, h_label

def compute_svd_target(H_label, R=32):
    """计算SVD目标值"""
    batch_size = H_label.shape[0]
    M, N = H_label.shape[1:3]
    
    U_target = torch.zeros(batch_size, M, R, 2)
    S_target = torch.zeros(batch_size, R)
    V_target = torch.zeros(batch_size, N, R, 2)
    
    for i in range(batch_size):
        # 转换为复数
        H_complex = torch.complex(H_label[i, :, :, 0], H_label[i, :, :, 1])
        
        # SVD分解
        U, S, Vh = torch.linalg.svd(H_complex, full_matrices=False)
        V = Vh.conj().T
        
        # 取前R个分量
        U_r = U[:, :R]
        S_r = S[:R]
        V_r = V[:, :R]
        
        # 转换为实数表示
        U_target[i] = torch.stack([U_r.real, U_r.imag], dim=-1)
        S_target[i] = S_r
        V_target[i] = torch.stack([V_r.real, V_r.imag], dim=-1)
    
    return U_target, S_target, V_target
