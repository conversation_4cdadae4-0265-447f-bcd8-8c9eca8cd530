import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np

class SimpleSVDNet(nn.Module):
    """简化的SVD网络 - 确保基本功能正常"""
    def __init__(self, M=64, N=64, R=32):
        super(SimpleSVDNet, self).__init__()
        self.M = M
        self.N = N
        self.R = R
        
        # 输入维度: M * N * 2 (实部+虚部)
        input_dim = M * N * 2
        
        # 编码器
        self.encoder = nn.Sequential(
            nn.Linear(input_dim, 1024),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(1024, 512),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(512, 256),
            nn.ReLU()
        )
        
        # U矩阵输出头 [M, R, 2]
        self.U_head = nn.Sequential(
            nn.Linear(256, M * R * 2),
            nn.Tanh()  # 限制输出范围
        )
        
        # V矩阵输出头 [N, R, 2]
        self.V_head = nn.Sequential(
            nn.Linear(256, N * R * 2),
            nn.Tanh()  # 限制输出范围
        )
        
        # S向量输出头 [R]
        self.S_head = nn.Sequential(
            nn.Linear(256, R),
            nn.Softplus()  # 确保为正
        )
        
    def normalize_matrix(self, matrix):
        """归一化矩阵列向量"""
        # matrix: [M/N, R, 2]
        real = matrix[..., 0]  # [M/N, R]
        imag = matrix[..., 1]  # [M/N, R]
        
        # 计算每列的模长
        norm = torch.sqrt(real**2 + imag**2 + 1e-8)  # [M/N, R]
        
        # 归一化
        real_norm = real / norm
        imag_norm = imag / norm
        
        return torch.stack([real_norm, imag_norm], dim=-1)
    
    def forward(self, x):
        """
        输入: x [M, N, 2] - 信道矩阵
        输出: U [M, R, 2], S [R], V [N, R, 2]
        """
        # 展平输入
        x_flat = x.view(-1)  # [M*N*2]
        
        # 编码
        features = self.encoder(x_flat)  # [256]
        
        # 生成U矩阵
        U_flat = self.U_head(features)  # [M*R*2]
        U = U_flat.view(self.M, self.R, 2)  # [M, R, 2]
        U = self.normalize_matrix(U)
        
        # 生成V矩阵
        V_flat = self.V_head(features)  # [N*R*2]
        V = V_flat.view(self.N, self.R, 2)  # [N, R, 2]
        V = self.normalize_matrix(V)
        
        # 生成S向量
        S = self.S_head(features)  # [R]
        S = S + 1e-6  # 确保为正
        
        return U, S, V

class SVDNet(nn.Module):
    """主要的SVD网络类 - 竞赛接口"""
    def __init__(self, M=64, N=64, R=32):
        super(SVDNet, self).__init__()
        self.simple_net = SimpleSVDNet(M, N, R)
        
    def forward(self, x):
        """
        输入: x [M, N, 2] - 信道矩阵 (实部+虚部)
        输出: U [M, R, 2], S [R], V [N, R, 2]
        """
        return self.simple_net(x)

# 测试函数
def test_simple_svd():
    """测试简化版SVD网络"""
    print("Testing SimpleSVDNet...")
    
    model = SVDNet()
    model.eval()
    
    # 创建测试输入
    H_test = torch.randn(64, 64, 2)
    
    with torch.no_grad():
        U, S, V = model(H_test)
    
    print(f"Input shape: {H_test.shape}")
    print(f"U shape: {U.shape}")
    print(f"S shape: {S.shape}")
    print(f"V shape: {V.shape}")
    
    # 检查输出范围
    print(f"U range: [{U.min():.4f}, {U.max():.4f}]")
    print(f"S range: [{S.min():.4f}, {S.max():.4f}]")
    print(f"V range: [{V.min():.4f}, {V.max():.4f}]")
    
    # 检查列向量归一化
    U_complex = torch.complex(U[..., 0], U[..., 1])
    V_complex = torch.complex(V[..., 0], V[..., 1])
    
    U_norms = torch.norm(U_complex, dim=0)
    V_norms = torch.norm(V_complex, dim=0)
    
    print(f"U column norms: {U_norms.mean():.4f} ± {U_norms.std():.4f}")
    print(f"V column norms: {V_norms.mean():.4f} ± {V_norms.std():.4f}")
    
    print("✓ SimpleSVDNet test completed!")
    
    return True

if __name__ == "__main__":
    test_simple_svd()
