print("Starting simple test...")

try:
    import torch
    print("PyTorch imported successfully")
    
    import solution
    print("Solution module imported successfully")
    
    model = solution.SVDNet()
    print("SVDNet created successfully")
    
    # Test with small input
    x = torch.randn(64, 64, 2)
    print(f"Input shape: {x.shape}")
    
    U, S, V = model(x)
    print(f"Output shapes: U={U.shape}, S={S.shape}, V={V.shape}")
    
    print("✓ Basic test passed!")
    
except Exception as e:
    print(f"✗ Test failed: {e}")
    import traceback
    traceback.print_exc()
