import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
import numpy as np
from solution import SVDNet
from data_augmentation import PhysicalChannelAugmentation, SVDDataset, compute_svd_target

class SVDLoss(nn.Module):
    """SVD损失函数 - 基于竞赛AE公式的简化版本"""

    def __init__(self, alpha=1.0, beta=0.1, gamma=0.1):
        super(SVDLoss, self).__init__()
        self.mse = nn.MSELoss()
        self.alpha = alpha  # 重构损失权重
        self.beta = beta    # U正交性损失权重 (备用)
        self.gamma = gamma  # V正交性损失权重 (备用)

    def forward(self, pred_U, pred_S, pred_V, H_label):
        """
        计算基于AE公式的损失
        AE = ||H_label - U*S*V^H||_F / ||H_label||_F + ||U^H*U - I||_F + ||V^H*V - I||_F

        由于使用硬正交性约束，正交性项应该接近0，主要优化重构项
        """
        batch_size = H_label.size(0)

        # 重构信道矩阵
        H_recon = self.reconstruct_channel(pred_U, pred_S, pred_V)

        # 计算归一化重构损失 (AE公式第一项)
        recon_error = torch.norm(H_label - H_recon, dim=(-2, -1))  # [batch]
        H_norm = torch.norm(H_label, dim=(-2, -1))  # [batch]
        normalized_recon_loss = torch.mean(recon_error / (H_norm + 1e-8))

        # 验证正交性 (应该很小，因为使用了硬约束)
        U_orth_loss = self.compute_orthogonality_loss(pred_U)
        V_orth_loss = self.compute_orthogonality_loss(pred_V)

        # 总损失
        total_loss = (self.alpha * normalized_recon_loss +
                     self.beta * U_orth_loss +
                     self.gamma * V_orth_loss)

        return total_loss, {
            'recon_loss': normalized_recon_loss.item(),
            'U_orth_loss': U_orth_loss.item(),
            'V_orth_loss': V_orth_loss.item()
        }
    
    def reconstruct_channel(self, U, S, V):
        """重构信道矩阵 H = U * S * V^H"""
        batch_size = U.size(0)

        # 转换为复数
        U_complex = torch.complex(U[..., 0], U[..., 1])  # [batch, M, R]
        V_complex = torch.complex(V[..., 0], V[..., 1])  # [batch, N, R]

        # 计算 U * S
        US = U_complex * S.unsqueeze(1)  # [batch, M, R]

        # 计算 U * S * V^H
        H_complex = torch.matmul(US, V_complex.conj().transpose(-2, -1))  # [batch, M, N]

        # 转换回实数表示
        H_recon = torch.stack([H_complex.real, H_complex.imag], dim=-1)

        return H_recon

    def compute_orthogonality_loss(self, matrix):
        """计算正交性损失 ||M^H * M - I||_F"""
        # matrix: [batch, size, rank, 2]
        batch_size = matrix.size(0)
        matrix_size = matrix.size(1)
        rank = matrix.size(2)

        # 转换为复数
        matrix_complex = torch.complex(matrix[..., 0], matrix[..., 1])

        # 计算 M^H * M
        MH_M = torch.matmul(matrix_complex.conj().transpose(-2, -1), matrix_complex)

        # 单位矩阵
        I = torch.eye(rank, dtype=MH_M.dtype, device=MH_M.device)
        I = I.unsqueeze(0).expand(batch_size, -1, -1)

        # 计算Frobenius范数
        orth_error = torch.norm(MH_M - I, dim=(-2, -1))

        return torch.mean(orth_error)

class SVDTrainer:
    """SVD网络训练器"""
    
    def __init__(self, model, device='cpu', learning_rate=1e-3):
        self.model = model.to(device)
        self.device = device
        self.criterion = SVDLoss()
        self.optimizer = optim.AdamW(model.parameters(), lr=learning_rate, weight_decay=1e-4)
        self.scheduler = optim.lr_scheduler.CosineAnnealingLR(self.optimizer, T_max=100)
        
    def train_epoch(self, dataloader):
        """训练一个epoch"""
        self.model.train()
        total_loss = 0.0
        num_batches = 0
        
        for batch_idx, (H_input, H_label) in enumerate(dataloader):
            H_input = H_input.to(self.device)
            H_label = H_label.to(self.device)
            
            self.optimizer.zero_grad()
            
            # 前向传播
            batch_size = H_input.size(0)
            total_loss_batch = 0.0
            
            for i in range(batch_size):
                # 单样本处理 (适配当前接口)
                pred_U, pred_S, pred_V = self.model(H_input[i])
                
                # 添加batch维度
                pred_U = pred_U.unsqueeze(0)
                pred_S = pred_S.unsqueeze(0)
                pred_V = pred_V.unsqueeze(0)
                
                # 计算损失
                loss = self.criterion(pred_U, pred_S, pred_V, H_label[i:i+1])
                total_loss_batch += loss
            
            # 平均损失
            avg_loss = total_loss_batch / batch_size
            
            # 反向传播
            avg_loss.backward()
            torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)
            self.optimizer.step()
            
            total_loss += avg_loss.item()
            num_batches += 1
            
            if batch_idx % 100 == 0:
                print(f'Batch {batch_idx}, Loss: {avg_loss.item():.6f}')
        
        return total_loss / num_batches
    
    def validate(self, dataloader):
        """验证"""
        self.model.eval()
        total_loss = 0.0
        num_batches = 0
        
        with torch.no_grad():
            for H_input, H_label in dataloader:
                H_input = H_input.to(self.device)
                H_label = H_label.to(self.device)
                
                batch_size = H_input.size(0)
                total_loss_batch = 0.0
                
                for i in range(batch_size):
                    pred_U, pred_S, pred_V = self.model(H_input[i])
                    
                    pred_U = pred_U.unsqueeze(0)
                    pred_S = pred_S.unsqueeze(0)
                    pred_V = pred_V.unsqueeze(0)
                    
                    loss = self.criterion(pred_U, pred_S, pred_V, H_label[i:i+1])
                    total_loss_batch += loss
                
                avg_loss = total_loss_batch / batch_size
                total_loss += avg_loss.item()
                num_batches += 1
        
        return total_loss / num_batches
    
    def train(self, train_loader, val_loader, epochs=100):
        """完整训练流程"""
        best_val_loss = float('inf')
        
        for epoch in range(epochs):
            print(f'\nEpoch {epoch+1}/{epochs}')
            
            # 训练
            train_loss = self.train_epoch(train_loader)
            
            # 验证
            val_loss = self.validate(val_loader)
            
            # 学习率调度
            self.scheduler.step()
            
            print(f'Train Loss: {train_loss:.6f}, Val Loss: {val_loss:.6f}')
            
            # 保存最佳模型
            if val_loss < best_val_loss:
                best_val_loss = val_loss
                torch.save(self.model.state_dict(), 'best_svd_model.pth')
                print(f'New best model saved with val loss: {val_loss:.6f}')

def load_data():
    """加载训练数据"""
    # 加载所有训练数据
    train_data_all = []
    train_label_all = []
    
    for i in range(1, 4):  # Round1TrainData1.npy, Round1TrainData2.npy, Round1TrainData3.npy
        train_data = np.load(f'CompetitionData1/Round1TrainData{i}.npy')
        train_label = np.load(f'CompetitionData1/Round1TrainLabel{i}.npy')
        
        train_data_all.append(train_data)
        train_label_all.append(train_label)
    
    # 合并数据
    train_data_combined = np.concatenate(train_data_all, axis=0)
    train_label_combined = np.concatenate(train_label_all, axis=0)
    
    print(f"Total training samples: {len(train_data_combined)}")
    
    return train_data_combined, train_label_combined

def main():
    """主训练函数"""
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")
    
    # 加载数据
    train_data, train_label = load_data()
    
    # 数据分割
    split_idx = int(0.9 * len(train_data))
    train_data_split = train_data[:split_idx]
    train_label_split = train_label[:split_idx]
    val_data = train_data[split_idx:]
    val_label = train_label[split_idx:]
    
    # 数据增强
    augmentation = PhysicalChannelAugmentation()
    
    # 创建数据集
    train_dataset = SVDDataset(train_data_split, train_label_split, augmentation)
    val_dataset = SVDDataset(val_data, val_label, None)
    
    # 创建数据加载器
    train_loader = DataLoader(train_dataset, batch_size=8, shuffle=True)
    val_loader = DataLoader(val_dataset, batch_size=8, shuffle=False)
    
    # 创建模型和训练器
    model = SVDNet(M=64, N=64, R=32)
    trainer = SVDTrainer(model, device, learning_rate=1e-3)
    
    # 开始训练
    trainer.train(train_loader, val_loader, epochs=50)

if __name__ == "__main__":
    main()
