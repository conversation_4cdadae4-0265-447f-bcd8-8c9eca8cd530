# AI使能SVD算子高性能实现

本项目完整实现了方案文档《面向大规模MIMO系统的AI使能SVD算子高性能实现战略蓝图》中提出的HCAN（混合卷积注意力网络）架构。

## 项目结构

```
├── solution.py              # 主要的SVD网络实现（竞赛接口）
├── data_augmentation.py     # 物理信息数据增强策略
├── training.py             # 训练相关组件
├── model_compression.py    # 模型压缩工具
├── complete_training.py    # 完整训练流水线
├── demo_code.py           # 竞赛提供的演示代码
└── README.md              # 项目说明
```

## 核心架构特性

### 1. 混合卷积注意力网络 (HCAN)

- **U-Net骨干网络**: 类U-Net的编码器-解码器架构，适合矩阵到矩阵的映射任务
- **CBAM注意力机制**: 集成卷积块注意力模块，包含通道注意力和空间注意力
- **硬正交性约束**: 通过李群参数化（矩阵指数）确保U和V矩阵严格满足酉矩阵条件

### 2. 物理信息数据增强

实现了基于无线信道物理特性的数据增强策略：

- **AWGN**: 加性复高斯白噪声，模拟接收机热噪声
- **相位噪声**: 维纳过程建模的本地振荡器相位噪声
- **多普勒频移**: 模拟用户移动导致的频率偏移
- **硬件非理想因素**: IQ不平衡、载波泄漏等
- **信道估计误差**: 模拟实际系统中的估计误差

### 3. 两阶段优化策略

#### 第一阶段：最大化精度
- 使用完整的HCAN架构
- 专注于最小化AE分数
- 应用物理信息数据增强提升鲁棒性

#### 第二阶段：效率优化
- **结构化剪枝**: 移除整个通道，直接减少MACs
- **权重SVD压缩**: 利用SVD分解压缩全连接层权重
- **微调**: 恢复压缩过程中损失的精度

## 关键技术创新

### 1. 硬正交性约束

```python
class OrthogonalLayer(nn.Module):
    """通过李群参数化实现硬正交性约束"""
    def forward(self, features):
        # 生成斜埃尔米特矩阵参数
        A_complex = torch.complex(real_part, imag_part)
        
        # 矩阵指数近似
        exp_A = I + A_complex + 0.5 * A_squared
        
        # QR分解确保严格正交性
        Q, R = torch.linalg.qr(exp_A)
        return Q_truncated
```

### 2. 基于AE公式的损失函数

```python
def forward(self, pred_U, pred_S, pred_V, H_label):
    # 归一化重构损失 (AE公式第一项)
    recon_error = torch.norm(H_label - H_recon, dim=(-2, -1))
    H_norm = torch.norm(H_label, dim=(-2, -1))
    normalized_recon_loss = torch.mean(recon_error / (H_norm + 1e-8))
    
    # 由于硬约束，正交性项接近0
    return normalized_recon_loss
```

### 3. CBAM注意力机制

```python
class CBAM(nn.Module):
    def forward(self, x):
        x = x * self.channel_attention(x)  # 通道注意力
        x = x * self.spatial_attention(x)  # 空间注意力
        return x
```

## 使用方法

### 1. 训练模型

```bash
python complete_training.py
```

这将执行完整的两阶段训练流程：
1. 第一阶段：精度最大化训练
2. 第二阶段：模型压缩与优化

### 2. 使用训练好的模型

```python
from solution import SVDNet
import torch

# 创建模型
model = SVDNet(M=64, N=64, R=32)
model.load_state_dict(torch.load('best_hcan_model.pth'))

# 推理
H_input = torch.randn(64, 64, 2)  # [M, N, 2]
U, S, V = model(H_input)          # [M,R,2], [R], [N,R,2]
```

### 3. 竞赛提交

使用 `demo_code.py` 进行最终测试和结果生成：

```bash
python demo_code.py
```

## 性能特点

### 1. 精度保证
- 硬正交性约束确保机器精度的酉矩阵输出
- 物理信息数据增强提升对噪声、相位误差等的鲁棒性
- 基于AE公式的损失函数直接优化竞赛指标

### 2. 效率优化
- 结构化剪枝减少实际计算量
- 权重SVD压缩降低参数数量
- 两阶段策略在精度-效率间找到最优平衡点

### 3. 工程化设计
- 模块化架构便于调试和优化
- 完整的训练流水线支持端到端训练
- 符合竞赛接口规范的标准化输出

## 理论基础

本实现基于以下理论和技术：

1. **SVD分解理论**: 奇异值分解在MIMO系统中的应用
2. **李群理论**: 通过矩阵指数实现酉矩阵参数化
3. **注意力机制**: CBAM在计算机视觉中的成功应用
4. **模型压缩**: 结构化剪枝和低秩分解技术
5. **物理信道建模**: 无线通信中的各种物理损伤

## 竞赛适配

- **输入格式**: [M, N, 2] 复数信道矩阵
- **输出格式**: U [M, R, 2], S [R], V [N, R, 2]
- **评估指标**: AE (近似误差) 和 MACs (乘加运算次数)
- **约束条件**: 禁用内置SVD/EVD函数

## 扩展性

该架构具有良好的扩展性：

1. **不同矩阵尺寸**: 通过调整M, N, R参数适配
2. **更多物理损伤**: 可添加更多信道损伤模型
3. **高级压缩技术**: 可集成量化、知识蒸馏等技术
4. **硬件优化**: 可针对特定硬件平台进行优化

## 总结

本项目完整实现了方案文档中提出的战略蓝图，通过HCAN架构、硬正交性约束、物理信息数据增强和两阶段优化策略，在保证高精度的同时实现了高效率，为大规模MIMO系统的AI使能SVD算子提供了完整的解决方案。
